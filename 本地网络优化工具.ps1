# 本地网络优化工具
# 用于诊断和优化本地网络连接质量

param(
    [switch]$AutoFix,           # 自动修复模式
    [switch]$DetailedReport,   # 详细报告模式
    [string]$LogFile = "network_optimization.log"
)

# 需要管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 此脚本需要管理员权限运行" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    pause
    exit 1
}

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Color = "White")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] $Message"
    Write-Host $logEntry -ForegroundColor $Color
    Add-Content -Path $LogFile -Value $logEntry
}

Write-Log "🚀 本地网络优化工具启动" "Green"
Write-Log "自动修复模式: $AutoFix" "Cyan"
Write-Log "=" * 60 "Gray"

# 1. 网络适配器诊断和优化
function Optimize-NetworkAdapters {
    Write-Log "📡 网络适配器诊断与优化" "Yellow"
    
    $adapters = Get-NetAdapter | Where-Object {$_.Status -eq "Up"}
    
    foreach ($adapter in $adapters) {
        Write-Log "检查适配器: $($adapter.Name)" "Cyan"
        
        # 检查适配器速度
        if ($adapter.LinkSpeed) {
            $speedMbps = [math]::Round($adapter.LinkSpeed / 1MB, 0)
            Write-Log "  当前速度: ${speedMbps} Mbps" "White"
            
            if ($speedMbps -lt 100) {
                Write-Log "  ⚠️  速度较低，建议检查网线或WiFi信号" "Yellow"
            }
        }
        
        # 检查网络适配器电源管理
        $powerMgmt = Get-NetAdapterPowerManagement -Name $adapter.Name -ErrorAction SilentlyContinue
        if ($powerMgmt -and $powerMgmt.AllowComputerToTurnOffDevice) {
            Write-Log "  ⚠️  发现电源管理问题：允许计算机关闭此设备" "Yellow"
            
            if ($AutoFix) {
                try {
                    Set-NetAdapterPowerManagement -Name $adapter.Name -AllowComputerToTurnOffDevice Disabled
                    Write-Log "  ✅ 已禁用网络适配器电源管理" "Green"
                } catch {
                    Write-Log "  ❌ 禁用电源管理失败: $($_.Exception.Message)" "Red"
                }
            } else {
                Write-Log "  💡 建议禁用此适配器的电源管理" "Cyan"
            }
        }
        
        # 检查网络适配器高级设置
        if ($DetailedReport) {
            $advancedProps = Get-NetAdapterAdvancedProperty -Name $adapter.Name -ErrorAction SilentlyContinue
            $importantProps = $advancedProps | Where-Object {
                $_.RegistryKeyword -in @("*FlowControl", "*InterruptModeration", "*RSS", "*JumboPacket")
            }
            
            foreach ($prop in $importantProps) {
                Write-Log "  高级属性 $($prop.DisplayName): $($prop.DisplayValue)" "Gray"
            }
        }
    }
}

# 2. DNS优化
function Optimize-DNS {
    Write-Log "🌐 DNS配置优化" "Yellow"
    
    # 获取当前DNS设置
    $currentDNS = Get-DnsClientServerAddress -AddressFamily IPv4 | Where-Object {$_.ServerAddresses.Count -gt 0}
    
    Write-Log "当前DNS配置:" "Cyan"
    foreach ($dns in $currentDNS) {
        Write-Log "  接口: $($dns.InterfaceAlias)" "White"
        Write-Log "  DNS服务器: $($dns.ServerAddresses -join ', ')" "White"
    }
    
    # 推荐的DNS服务器
    $recommendedDNS = @{
        "阿里云DNS" = @("*********", "*********")
        "腾讯DNS" = @("************", "***************") 
        "114DNS" = @("***************", "***************")
        "Google DNS" = @("*******", "*******")
        "Cloudflare DNS" = @("*******", "*******")
    }
    
    # 测试DNS响应速度
    Write-Log "测试DNS服务器响应速度:" "Cyan"
    $bestDNS = $null
    $bestTime = [double]::MaxValue
    
    foreach ($dnsProvider in $recommendedDNS.GetEnumerator()) {
        $primaryDNS = $dnsProvider.Value[0]
        try {
            $time = Measure-Command {
                [System.Net.Dns]::GetHostAddresses("www.baidu.com") | Out-Null
            }
            $responseTime = [math]::Round($time.TotalMilliseconds, 0)
            Write-Log "  $($dnsProvider.Key) ($primaryDNS): ${responseTime}ms" "White"
            
            if ($responseTime -lt $bestTime) {
                $bestTime = $responseTime
                $bestDNS = $dnsProvider
            }
        } catch {
            Write-Log "  $($dnsProvider.Key) ($primaryDNS): 测试失败" "Red"
        }
    }
    
    if ($bestDNS -and $AutoFix) {
        Write-Log "🎯 最佳DNS: $($bestDNS.Key) (${bestTime}ms)" "Green"
        
        # 应用最佳DNS到主要网络接口
        $mainAdapter = Get-NetAdapter | Where-Object {$_.Status -eq "Up" -and $_.MediaType -notlike "*Loopback*"} | Select-Object -First 1
        
        if ($mainAdapter) {
            try {
                Set-DnsClientServerAddress -InterfaceAlias $mainAdapter.Name -ServerAddresses $bestDNS.Value
                Write-Log "✅ 已将DNS更改为 $($bestDNS.Key)" "Green"
                
                # 清除DNS缓存
                Clear-DnsClientCache
                Write-Log "✅ 已清除DNS缓存" "Green"
            } catch {
                Write-Log "❌ DNS设置失败: $($_.Exception.Message)" "Red"
            }
        }
    } elseif ($bestDNS) {
        Write-Log "💡 建议使用 $($bestDNS.Key): $($bestDNS.Value -join ', ')" "Cyan"
    }
}

# 3. 网络性能优化
function Optimize-NetworkPerformance {
    Write-Log "⚡ 网络性能优化" "Yellow"
    
    # TCP窗口自动调优
    $tcpSettings = Get-NetTCPSetting -SettingName "Internet"
    Write-Log "当前TCP自动调优级别: $($tcpSettings.AutoTuningLevelLocal)" "White"
    
    if ($tcpSettings.AutoTuningLevelLocal -ne "Normal" -and $AutoFix) {
        try {
            Set-NetTCPSetting -SettingName "Internet" -AutoTuningLevelLocal Normal
            Write-Log "✅ 已启用TCP窗口自动调优" "Green"
        } catch {
            Write-Log "❌ TCP设置失败: $($_.Exception.Message)" "Red"
        }
    }
    
    # 检查网络拥塞控制
    if ($DetailedReport) {
        $congestionProvider = Get-NetTCPSetting -SettingName "Internet" | Select-Object CongestionProvider
        Write-Log "拥塞控制算法: $($congestionProvider.CongestionProvider)" "Gray"
    }
    
    # 检查网络缓冲区设置
    try {
        $receiveBuffer = (Get-NetTCPSetting -SettingName "Internet").ReceiveWindow
        $sendBuffer = (Get-NetTCPSetting -SettingName "Internet").InitialCongestionWindow
        Write-Log "接收窗口大小: $receiveBuffer" "Gray"
        Write-Log "初始拥塞窗口: $sendBuffer" "Gray"
    } catch {
        Write-Log "无法获取缓冲区信息" "Gray"
    }
}

# 4. 防火墙和安全优化
function Optimize-FirewallSecurity {
    Write-Log "🛡️  防火墙和安全检查" "Yellow"
    
    # 检查Windows防火墙状态
    $firewallProfiles = Get-NetFirewallProfile
    foreach ($profile in $firewallProfiles) {
        $status = if ($profile.Enabled) { "启用" } else { "禁用" }
        Write-Log "防火墙配置文件 $($profile.Name): $status" "White"
        
        if ($profile.Enabled -and $profile.DefaultInboundAction -eq "Block") {
            Write-Log "  入站规则: 默认阻止" "Gray"
        }
    }
    
    # 检查RDP防火墙规则
    $rdpRules = Get-NetFirewallRule | Where-Object {$_.DisplayName -like "*远程桌面*" -or $_.DisplayName -like "*Remote Desktop*"}
    
    if ($rdpRules) {
        Write-Log "远程桌面防火墙规则:" "Cyan"
        foreach ($rule in $rdpRules) {
            $status = if ($rule.Enabled) { "启用" } else { "禁用" }
            Write-Log "  $($rule.DisplayName): $status" "White"
        }
    } else {
        Write-Log "⚠️  未找到远程桌面防火墙规则" "Yellow"
    }
}

# 5. 系统资源检查
function Check-SystemResources {
    Write-Log "💻 系统资源检查" "Yellow"
    
    # CPU使用率
    $cpu = Get-WmiObject -Class Win32_Processor | Measure-Object -Property LoadPercentage -Average
    Write-Log "CPU平均使用率: $([math]::Round($cpu.Average, 1))%" "White"
    
    # 内存使用情况
    $memory = Get-WmiObject -Class Win32_OperatingSystem
    $totalMemory = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
    $freeMemory = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
    $usedMemory = $totalMemory - $freeMemory
    $memoryUsage = [math]::Round(($usedMemory / $totalMemory) * 100, 1)
    
    Write-Log "内存使用情况: ${usedMemory}GB / ${totalMemory}GB (${memoryUsage}%)" "White"
    
    if ($memoryUsage -gt 80) {
        Write-Log "⚠️  内存使用率较高，可能影响网络性能" "Yellow"
    }
    
    # 磁盘使用情况
    $disks = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3}
    foreach ($disk in $disks) {
        $freeSpace = [math]::Round($disk.FreeSpace / 1GB, 2)
        $totalSpace = [math]::Round($disk.Size / 1GB, 2)
        $usedPercent = [math]::Round((($totalSpace - $freeSpace) / $totalSpace) * 100, 1)
        Write-Log "磁盘 $($disk.DeviceID) 使用率: ${usedPercent}% (剩余 ${freeSpace}GB)" "White"
    }
}

# 主执行流程
Write-Log "开始网络诊断和优化..." "Green"

Optimize-NetworkAdapters
Write-Log "" "White"

Optimize-DNS  
Write-Log "" "White"

Optimize-NetworkPerformance
Write-Log "" "White"

Optimize-FirewallSecurity
Write-Log "" "White"

Check-SystemResources
Write-Log "" "White"

# 生成优化建议
Write-Log "=" * 60 "Gray"
Write-Log "🎯 网络优化建议总结" "Green"
Write-Log "=" * 60 "Gray"

Write-Log "1. 🔧 硬件层面优化:" "Yellow"
Write-Log "   - 使用有线连接替代WiFi（如可能）" "White"
Write-Log "   - 检查网线质量，使用Cat6或更高规格" "White"
Write-Log "   - 确保路由器固件为最新版本" "White"
Write-Log "   - 将设备靠近路由器或使用WiFi信号放大器" "White"

Write-Log "2. 🌐 网络配置优化:" "Yellow"
Write-Log "   - 使用最快的DNS服务器" "White"
Write-Log "   - 禁用网络适配器电源管理" "White"
Write-Log "   - 启用TCP窗口自动调优" "White"
Write-Log "   - 定期重启路由器清除缓存" "White"

Write-Log "3. 🛡️  安全和防火墙:" "Yellow"
Write-Log "   - 确保远程桌面防火墙规则正确配置" "White"
Write-Log "   - 考虑使用VPN提高连接稳定性" "White"
Write-Log "   - 定期更新系统和驱动程序" "White"

Write-Log "4. ⚡ 性能优化:" "Yellow"
Write-Log "   - 关闭不必要的后台程序" "White"
Write-Log "   - 定期清理系统垃圾文件" "White"
Write-Log "   - 监控系统资源使用情况" "White"
Write-Log "   - 考虑升级硬件（内存、SSD）" "White"

Write-Log "=" * 60 "Gray"
Write-Log "✅ 网络优化完成！日志已保存到: $LogFile" "Green"

if (-not $AutoFix) {
    Write-Log "💡 提示: 使用 -AutoFix 参数可自动应用部分优化设置" "Cyan"
}

Write-Log "建议重启计算机以使所有更改生效" "Yellow"
