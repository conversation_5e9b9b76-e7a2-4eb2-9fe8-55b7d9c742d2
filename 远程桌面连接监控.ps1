# 远程桌面连接监控和诊断脚本
# 用于监控网络连接质量和诊断RDP连接问题

param(
    [Parameter(Mandatory=$true)]
    [string]$TargetIP = "************",  # 您截图中的IP地址
    
    [int]$PingInterval = 5,              # ping间隔（秒）
    [int]$MonitorDuration = 300,         # 监控时长（秒）
    [string]$LogFile = "rdp_monitor.log" # 日志文件
)

# 创建日志函数
function Write-Log {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] $Message"
    Write-Host $logEntry
    Add-Content -Path $LogFile -Value $logEntry
}

# 检查RDP端口连通性
function Test-RDPPort {
    param([string]$ComputerName, [int]$Port = 3389)
    
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $connect = $tcpClient.BeginConnect($ComputerName, $Port, $null, $null)
        $wait = $connect.AsyncWaitHandle.WaitOne(3000, $false)
        
        if ($wait) {
            $tcpClient.EndConnect($connect)
            $tcpClient.Close()
            return $true
        } else {
            $tcpClient.Close()
            return $false
        }
    } catch {
        return $false
    }
}

# 获取网络统计信息
function Get-NetworkStats {
    $stats = Get-NetAdapterStatistics | Where-Object {$_.Name -notlike "*Loopback*"}
    return $stats | Select-Object Name, BytesReceived, BytesSent, PacketsReceived, PacketsSent
}

Write-Log "开始监控远程桌面连接到 $TargetIP"
Write-Log "监控时长: $MonitorDuration 秒, Ping间隔: $PingInterval 秒"
Write-Log "日志文件: $LogFile"
Write-Log "=" * 50

$startTime = Get-Date
$endTime = $startTime.AddSeconds($MonitorDuration)
$pingCount = 0
$successfulPings = 0
$failedPings = 0
$rdpTestCount = 0
$rdpSuccessCount = 0

# 初始网络统计
$initialStats = Get-NetworkStats

while ((Get-Date) -lt $endTime) {
    $pingCount++
    
    # 执行ping测试
    $pingResult = Test-Connection -ComputerName $TargetIP -Count 1 -Quiet
    
    if ($pingResult) {
        $successfulPings++
        $ping = Test-Connection -ComputerName $TargetIP -Count 1
        $responseTime = $ping.ResponseTime
        Write-Log "✅ Ping成功 - 响应时间: ${responseTime}ms"
    } else {
        $failedPings++
        Write-Log "❌ Ping失败 - 无响应"
    }
    
    # 每10次ping测试一次RDP端口
    if ($pingCount % 2 -eq 0) {
        $rdpTestCount++
        $rdpTest = Test-RDPPort -ComputerName $TargetIP
        
        if ($rdpTest) {
            $rdpSuccessCount++
            Write-Log "✅ RDP端口(3389)连通"
        } else {
            Write-Log "❌ RDP端口(3389)不通"
        }
    }
    
    # 检查网络接口状态
    $networkAdapters = Get-NetAdapter | Where-Object {$_.Status -eq "Up"}
    $activeAdapters = $networkAdapters.Count
    Write-Log "📡 活跃网络适配器数量: $activeAdapters"
    
    Start-Sleep -Seconds $PingInterval
}

# 最终统计
$finalStats = Get-NetworkStats
Write-Log "=" * 50
Write-Log "监控完成 - 最终统计报告"
Write-Log "=" * 50

$pingSuccessRate = [math]::Round(($successfulPings / $pingCount) * 100, 2)
$rdpSuccessRate = if ($rdpTestCount -gt 0) { [math]::Round(($rdpSuccessCount / $rdpTestCount) * 100, 2) } else { 0 }

Write-Log "📊 Ping统计:"
Write-Log "   总计: $pingCount 次"
Write-Log "   成功: $successfulPings 次"
Write-Log "   失败: $failedPings 次"
Write-Log "   成功率: $pingSuccessRate%"

Write-Log "📊 RDP端口测试统计:"
Write-Log "   总计: $rdpTestCount 次"
Write-Log "   成功: $rdpSuccessCount 次"
Write-Log "   成功率: $rdpSuccessRate%"

# 网络流量变化
Write-Log "📊 网络流量统计:"
foreach ($adapter in $finalStats) {
    $initial = $initialStats | Where-Object {$_.Name -eq $adapter.Name}
    if ($initial) {
        $receivedDiff = $adapter.BytesReceived - $initial.BytesReceived
        $sentDiff = $adapter.BytesSent - $initial.BytesSent
        Write-Log "   $($adapter.Name): 接收 $([math]::Round($receivedDiff/1MB, 2))MB, 发送 $([math]::Round($sentDiff/1MB, 2))MB"
    }
}

# 建议
Write-Log "=" * 50
Write-Log "🔧 连接问题诊断建议:"

if ($pingSuccessRate -lt 95) {
    Write-Log "⚠️  网络连接不稳定 (Ping成功率: $pingSuccessRate%)"
    Write-Log "   建议: 检查网络连接、更换DNS服务器、联系网络服务提供商"
}

if ($rdpSuccessRate -lt 90) {
    Write-Log "⚠️  RDP端口连接不稳定 (成功率: $rdpSuccessRate%)"
    Write-Log "   建议: 检查防火墙设置、确认RDP服务运行状态、检查端口转发"
}

if ($pingSuccessRate -ge 95 -and $rdpSuccessRate -ge 90) {
    Write-Log "✅ 网络连接良好"
    Write-Log "   如仍有断线问题，建议检查:"
    Write-Log "   - 远程桌面会话超时设置"
    Write-Log "   - 服务器资源使用情况"
    Write-Log "   - 客户端电源管理设置"
}

Write-Log "=" * 50
Write-Log "监控日志已保存到: $LogFile"
