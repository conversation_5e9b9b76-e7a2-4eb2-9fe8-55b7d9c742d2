@echo off
chcp 65001 >nul
title 简化网络优化工具

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 需要管理员权限运行此脚本
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ==========================================
echo 🚀 简化网络优化工具
echo ==========================================
echo.

echo 此工具将执行以下优化操作:
echo ✓ 网络适配器诊断
echo ✓ DNS优化设置
echo ✓ 网络协议重置
echo ✓ 性能参数调优
echo ✓ 连通性测试
echo.

set /p confirm="是否继续执行网络优化? (Y/N): "
if /i not "%confirm%"=="Y" goto exit

echo.
echo 开始网络优化...
echo.

:: 1. 网络适配器诊断
echo ==========================================
echo 📡 网络适配器诊断
echo ==========================================

echo 当前活跃的网络适配器:
wmic path win32_networkadapter where "NetConnectionStatus=2" get Name,NetConnectionID,Speed /format:table

echo.
echo 检查网络适配器电源管理设置...
powercfg /devicequery wake_armed | findstr /i "网络\|ethernet\|wifi\|wireless" >nul
if %errorlevel% equ 0 (
    echo ⚠️  发现网络设备允许唤醒计算机，可能影响连接稳定性
    echo 建议在设备管理器中禁用网络适配器的电源管理
) else (
    echo ✅ 网络适配器电源管理设置正常
)

echo.

:: 2. DNS优化
echo ==========================================
echo 🌐 DNS优化设置
echo ==========================================

echo 当前DNS配置:
ipconfig /all | findstr /i "dns服务器"

echo.
echo 测试不同DNS服务器的响应速度...

echo 测试阿里云DNS (*********)...
ping -n 1 ********* >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 阿里云DNS响应正常
    set best_dns=*********
    set backup_dns=*********
) else (
    echo ❌ 阿里云DNS无响应
)

echo 测试腾讯DNS (************)...
ping -n 1 ************ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 腾讯DNS响应正常
    if not defined best_dns (
        set best_dns=************
        set backup_dns=***************
    )
) else (
    echo ❌ 腾讯DNS无响应
)

echo 测试114DNS (***************)...
ping -n 1 *************** >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 114DNS响应正常
    if not defined best_dns (
        set best_dns=***************
        set backup_dns=***************
    )
) else (
    echo ❌ 114DNS无响应
)

if defined best_dns (
    echo.
    echo 🎯 设置最佳DNS服务器: %best_dns%
    
    :: 设置以太网DNS
    netsh interface ip set dns "以太网" static %best_dns% primary >nul 2>&1
    netsh interface ip add dns "以太网" %backup_dns% index=2 >nul 2>&1
    
    :: 设置WiFi DNS
    netsh interface ip set dns "WLAN" static %best_dns% primary >nul 2>&1
    netsh interface ip add dns "WLAN" %backup_dns% index=2 >nul 2>&1
    
    :: 设置无线网络连接DNS
    netsh interface ip set dns "无线网络连接" static %best_dns% primary >nul 2>&1
    netsh interface ip add dns "无线网络连接" %backup_dns% index=2 >nul 2>&1
    
    echo ✅ DNS服务器设置完成
) else (
    echo ⚠️  所有测试的DNS服务器都无响应，保持当前设置
)

echo.

:: 3. 网络协议重置
echo ==========================================
echo 🔄 网络协议重置
echo ==========================================

echo 重置Winsock目录...
netsh winsock reset >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Winsock重置成功
) else (
    echo ❌ Winsock重置失败
)

echo 重置TCP/IP协议栈...
netsh int ip reset >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ TCP/IP重置成功
) else (
    echo ❌ TCP/IP重置失败
)

echo 清除DNS缓存...
ipconfig /flushdns >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ DNS缓存清除成功
) else (
    echo ❌ DNS缓存清除失败
)

echo 清除ARP缓存...
arp -d * >nul 2>&1
echo ✅ ARP缓存清除完成

echo.

:: 4. 性能参数调优
echo ==========================================
echo ⚡ 网络性能参数调优
echo ==========================================

echo 启用TCP窗口自动调优...
netsh int tcp set global autotuninglevel=normal >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ TCP自动调优启用成功
) else (
    echo ❌ TCP自动调优设置失败
)

echo 优化TCP性能参数...
netsh int tcp set global chimney=enabled >nul 2>&1
netsh int tcp set global rss=enabled >nul 2>&1
netsh int tcp set global netdma=enabled >nul 2>&1
echo ✅ TCP性能参数优化完成

echo 优化网络缓冲区设置...
netsh int tcp set global maxsynretransmissions=2 >nul 2>&1
netsh int tcp set global initialrto=3000 >nul 2>&1
echo ✅ 网络缓冲区优化完成

echo.

:: 5. 重新获取网络配置
echo ==========================================
echo 🔄 重新获取网络配置
echo ==========================================

echo 释放IP地址...
ipconfig /release >nul 2>&1
echo ✅ IP地址释放完成

echo 重新获取IP地址...
ipconfig /renew >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ IP地址获取成功
) else (
    echo ❌ IP地址获取失败
)

echo 注册DNS...
ipconfig /registerdns >nul 2>&1
echo ✅ DNS注册完成

echo.

:: 6. 连通性测试
echo ==========================================
echo 🌐 网络连通性测试
echo ==========================================

echo 测试本地网关连通性...
for /f "tokens=3" %%i in ('route print ^| findstr "0.0.0.0.*0.0.0.0"') do (
    ping -n 2 %%i >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ 网关连接正常 (%%i)
    ) else (
        echo ❌ 网关连接失败 (%%i)
    )
    goto :gateway_test_done
)
:gateway_test_done

echo 测试DNS解析功能...
nslookup www.baidu.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ DNS解析功能正常
) else (
    echo ❌ DNS解析功能异常
)

echo 测试外网连通性...
ping -n 2 www.baidu.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 外网连接正常
) else (
    echo ❌ 外网连接失败
)

echo.

:: 7. 系统资源检查
echo ==========================================
echo 💻 系统资源检查
echo ==========================================

echo CPU使用率:
wmic cpu get loadpercentage /value | findstr LoadPercentage

echo.
echo 内存使用情况:
for /f "tokens=2 delims==" %%i in ('wmic OS get TotalVisibleMemorySize /value ^| findstr "="') do set total_memory=%%i
for /f "tokens=2 delims==" %%i in ('wmic OS get FreePhysicalMemory /value ^| findstr "="') do set free_memory=%%i
set /a used_memory=%total_memory%-%free_memory%
set /a memory_percent=%used_memory%*100/%total_memory%
echo 内存使用率: %memory_percent%%%

echo.
echo 磁盘使用情况:
wmic logicaldisk where "DriveType=3" get Size,FreeSpace,DeviceID /format:table

echo.

:: 8. 优化结果总结
echo ==========================================
echo ✅ 网络优化完成！
echo ==========================================
echo.
echo 🎯 已完成的优化项目:
echo ✓ 网络适配器状态检查
echo ✓ DNS服务器优化设置
echo ✓ 网络协议栈重置
echo ✓ TCP性能参数调优
echo ✓ 网络缓存清理
echo ✓ IP配置重新获取
echo ✓ 连通性测试验证
echo.
echo 💡 建议操作:
echo 1. 重启计算机以使所有更改完全生效
echo 2. 如果问题仍然存在，检查硬件连接
echo 3. 考虑更新网络驱动程序
echo 4. 联系网络服务提供商检查线路质量
echo.

set /p restart="是否现在重启计算机? (Y/N): "
if /i "%restart%"=="Y" (
    echo 正在重启计算机...
    shutdown /r /t 10 /c "网络优化完成，系统将在10秒后重启"
    echo 系统将在10秒后自动重启...
    echo 按任意键取消重启
    pause >nul
    shutdown /a
    echo 重启已取消
) else (
    echo 请稍后手动重启计算机以使更改完全生效
)

:exit
echo.
pause
exit /b 0
