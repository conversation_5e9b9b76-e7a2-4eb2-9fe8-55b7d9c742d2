@echo off
chcp 65001 >nul
title 网络状态检查演示

echo ==========================================
echo 🌐 网络状态检查演示
echo ==========================================
echo.

echo 📊 当前网络配置信息:
echo ==========================================
ipconfig | findstr /i "ipv4\|默认网关\|子网掩码"

echo.
echo 📊 DNS配置信息:
echo ==========================================
ipconfig /all | findstr /i "dns服务器" | head -5

echo.
echo 📊 网络连通性测试:
echo ==========================================
echo 测试百度连通性...
ping -n 2 www.baidu.com

echo.
echo 测试阿里云DNS...
ping -n 2 *********

echo.
echo 📊 活跃网络连接:
echo ==========================================
netstat -an | findstr "ESTABLISHED" | find /c "ESTABLISHED"

echo.
echo ✅ 网络状态检查完成！
echo.
pause
