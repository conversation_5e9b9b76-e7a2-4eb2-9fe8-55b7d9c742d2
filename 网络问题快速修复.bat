@echo off
chcp 65001 >nul
title 网络问题快速修复工具

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 需要管理员权限运行此脚本
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ==========================================
echo 🚀 网络问题快速修复工具
echo ==========================================
echo.

echo 正在执行网络修复操作，请稍候...
echo.

echo ==========================================
echo 1. 重置网络配置
echo ==========================================

echo 📡 重置Winsock目录...
netsh winsock reset
if %errorlevel% equ 0 (
    echo ✅ Winsock重置成功
) else (
    echo ❌ Winsock重置失败
)

echo 📡 重置TCP/IP协议栈...
netsh int ip reset
if %errorlevel% equ 0 (
    echo ✅ TCP/IP重置成功
) else (
    echo ❌ TCP/IP重置失败
)

echo 📡 重置防火墙设置...
netsh advfirewall reset
if %errorlevel% equ 0 (
    echo ✅ 防火墙重置成功
) else (
    echo ❌ 防火墙重置失败
)

echo.

echo ==========================================
echo 2. 清理网络缓存
echo ==========================================

echo 🗑️  清除DNS缓存...
ipconfig /flushdns
if %errorlevel% equ 0 (
    echo ✅ DNS缓存清除成功
) else (
    echo ❌ DNS缓存清除失败
)

echo 🗑️  清除ARP缓存...
arp -d *
if %errorlevel% equ 0 (
    echo ✅ ARP缓存清除成功
) else (
    echo ❌ ARP缓存清除失败
)

echo 🗑️  清除NetBIOS缓存...
nbtstat -R
nbtstat -RR
echo ✅ NetBIOS缓存清除完成

echo.

echo ==========================================
echo 3. 重新获取网络配置
echo ==========================================

echo 🔄 释放IP地址...
ipconfig /release
echo ✅ IP地址释放完成

echo 🔄 重新获取IP地址...
ipconfig /renew
if %errorlevel% equ 0 (
    echo ✅ IP地址获取成功
) else (
    echo ❌ IP地址获取失败
)

echo 🔄 注册DNS...
ipconfig /registerdns
echo ✅ DNS注册完成

echo.

echo ==========================================
echo 4. 优化网络设置
echo ==========================================

echo ⚡ 启用TCP窗口自动调优...
netsh int tcp set global autotuninglevel=normal
if %errorlevel% equ 0 (
    echo ✅ TCP自动调优启用成功
) else (
    echo ❌ TCP自动调优设置失败
)

echo ⚡ 优化TCP设置...
netsh int tcp set global chimney=enabled
netsh int tcp set global rss=enabled
netsh int tcp set global netdma=enabled
echo ✅ TCP优化设置完成

echo ⚡ 设置DNS服务器为阿里云DNS...
netsh interface ip set dns "以太网" static ********* primary
netsh interface ip add dns "以太网" ********* index=2
netsh interface ip set dns "WLAN" static ********* primary
netsh interface ip add dns "WLAN" ********* index=2
echo ✅ DNS服务器设置完成

echo.

echo ==========================================
echo 5. 重启网络服务
echo ==========================================

echo 🔄 重启网络相关服务...

echo 重启DNS客户端服务...
net stop dnscache
net start dnscache
if %errorlevel% equ 0 (
    echo ✅ DNS客户端服务重启成功
) else (
    echo ❌ DNS客户端服务重启失败
)

echo 重启DHCP客户端服务...
net stop dhcp
net start dhcp
if %errorlevel% equ 0 (
    echo ✅ DHCP客户端服务重启成功
) else (
    echo ❌ DHCP客户端服务重启失败
)

echo 重启网络连接服务...
net stop netman
net start netman
if %errorlevel% equ 0 (
    echo ✅ 网络连接服务重启成功
) else (
    echo ❌ 网络连接服务重启失败
)

echo.

echo ==========================================
echo 6. 网络连通性测试
echo ==========================================

echo 🌐 测试网络连通性...

echo 测试本地网关...
for /f "tokens=3" %%i in ('route print ^| findstr "0.0.0.0.*0.0.0.0"') do (
    ping -n 2 %%i >nul
    if !errorlevel! equ 0 (
        echo ✅ 网关连接正常 (%%i)
    ) else (
        echo ❌ 网关连接失败 (%%i)
    )
    goto :gateway_done
)
:gateway_done

echo 测试DNS解析...
nslookup www.baidu.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ DNS解析正常
) else (
    echo ❌ DNS解析失败
)

echo 测试外网连通性...
ping -n 2 www.baidu.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 外网连接正常
) else (
    echo ❌ 外网连接失败
)

echo.

echo ==========================================
echo 7. 显示当前网络状态
echo ==========================================

echo 📊 当前网络配置:
ipconfig /all | findstr /i "ipv4\|默认网关\|DNS"

echo.
echo 📊 活跃网络连接:
netstat -an | findstr "ESTABLISHED" | find /c "ESTABLISHED" > temp.txt
set /p connections=<temp.txt
del temp.txt
echo 当前活跃连接数: %connections%

echo.

echo ==========================================
echo ✅ 网络修复完成！
echo ==========================================
echo.
echo 🎯 修复操作总结:
echo ✓ 重置了网络协议栈
echo ✓ 清理了所有网络缓存
echo ✓ 重新获取了IP配置
echo ✓ 优化了TCP性能设置
echo ✓ 设置了快速DNS服务器
echo ✓ 重启了关键网络服务
echo.
echo 💡 建议操作:
echo 1. 重启计算机以确保所有更改生效
echo 2. 如果问题仍然存在，请检查硬件连接
echo 3. 考虑更新网络驱动程序
echo 4. 联系网络服务提供商检查线路
echo.
echo ⚠️  注意: 某些设置可能需要重启后才能完全生效
echo.

set /p restart="是否现在重启计算机? (Y/N): "
if /i "%restart%"=="Y" (
    echo 正在重启计算机...
    shutdown /r /t 10 /c "网络修复完成，系统将在10秒后重启"
    echo 系统将在10秒后自动重启...
    echo 按任意键取消重启
    pause >nul
    shutdown /a
    echo 重启已取消
) else (
    echo 请稍后手动重启计算机以使更改完全生效
)

echo.
pause
