@echo off
chcp 65001 >nul
title 网络优化工具启动器

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 需要管理员权限运行此脚本
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ==========================================
echo 🚀 网络优化工具启动器
echo ==========================================
echo.

echo 检测到PowerShell执行策略限制问题
echo 正在使用安全方式启动网络优化工具...
echo.

echo 选择要执行的操作:
echo.
echo 1. 快速网络修复 (推荐先试)
echo 2. 深度网络诊断和优化
echo 3. 远程桌面连接监控
echo 4. 查看当前网络状态
echo 5. 退出
echo.

set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto quick_fix
if "%choice%"=="2" goto deep_optimize
if "%choice%"=="3" goto rdp_monitor
if "%choice%"=="4" goto network_status
if "%choice%"=="5" goto exit
goto invalid_choice

:quick_fix
echo.
echo 🔧 执行快速网络修复...
call "网络问题快速修复.bat"
goto end

:deep_optimize
echo.
echo 🔍 启动深度网络诊断和优化...
echo 正在检查文件位置...

:: 检查PowerShell脚本是否存在
if exist "本地网络优化工具.ps1" (
    echo ✅ 找到PowerShell脚本文件
    echo 正在绕过PowerShell执行策略限制...
    PowerShell -ExecutionPolicy Bypass -File "本地网络优化工具.ps1" -AutoFix -DetailedReport
) else (
    echo ❌ 未找到 "本地网络优化工具.ps1" 文件
    echo.
    echo 💡 解决方案：
    echo 1. 确保所有文件都在同一个文件夹中
    echo 2. 或者使用内置的网络诊断功能
    echo.
    echo 是否使用内置网络诊断? (Y/N)
    set /p use_builtin="请选择: "
    if /i "%use_builtin%"=="Y" (
        call :builtin_network_diagnostic
    )
)
goto end

:rdp_monitor
echo.
echo 📊 启动远程桌面连接监控...

:: 检查PowerShell脚本是否存在
if exist "远程桌面连接监控.ps1" (
    set /p target_ip="请输入要监控的远程IP地址 (默认: ************): "
    if "%target_ip%"=="" set target_ip=************

    set /p duration="请输入监控时长(秒) (默认: 300): "
    if "%duration%"=="" set duration=300

    echo 正在启动监控，目标: %target_ip%，时长: %duration%秒...
    PowerShell -ExecutionPolicy Bypass -File "远程桌面连接监控.ps1" -TargetIP "%target_ip%" -MonitorDuration %duration%
) else (
    echo ❌ 未找到 "远程桌面连接监控.ps1" 文件
    echo 使用内置的连接测试功能...
    call :builtin_connection_test
)
goto end

:network_status
echo.
echo 📊 当前网络状态检查
echo ==========================================

echo 🌐 网络适配器状态:
wmic path win32_networkadapter where "NetConnectionStatus=2" get Name,NetConnectionID,Speed /format:table

echo.
echo 🌐 IP配置信息:
ipconfig | findstr /i "ipv4\|默认网关\|子网掩码"

echo.
echo 🌐 DNS配置:
ipconfig /all | findstr /i "dns"

echo.
echo 🌐 网络连通性测试:
echo 测试网关连通性...
for /f "tokens=3" %%i in ('route print ^| findstr "0.0.0.0.*0.0.0.0"') do (
    ping -n 2 %%i >nul
    if !errorlevel! equ 0 (
        echo ✅ 网关连接正常 (%%i)
    ) else (
        echo ❌ 网关连接失败 (%%i)
    )
    goto :gateway_test_done
)
:gateway_test_done

echo 测试DNS解析...
nslookup www.baidu.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ DNS解析正常
) else (
    echo ❌ DNS解析失败
)

echo 测试外网连通性...
ping -n 2 www.baidu.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 外网连接正常
) else (
    echo ❌ 外网连接失败
)

echo.
echo 🌐 活跃网络连接数:
netstat -an | findstr "ESTABLISHED" | find /c "ESTABLISHED"

goto end

:invalid_choice
echo.
echo ❌ 无效选择，请输入 1-5 之间的数字
echo.
goto start

:end
echo.
echo ==========================================
echo ✅ 操作完成！
echo ==========================================
echo.

:: 内置网络诊断功能
:builtin_network_diagnostic
echo.
echo 🔍 内置网络诊断功能
echo ==========================================

echo 📡 检查网络适配器状态...
wmic path win32_networkadapter where "NetConnectionStatus=2" get Name,NetConnectionID,Speed /format:table

echo.
echo 🌐 当前IP配置...
ipconfig | findstr /i "ipv4\|默认网关\|子网掩码"

echo.
echo 🌐 DNS配置...
ipconfig /all | findstr /i "dns服务器"

echo.
echo 🔧 执行网络重置操作...
echo 清除DNS缓存...
ipconfig /flushdns

echo 重置TCP/IP...
netsh int ip reset

echo 重置Winsock...
netsh winsock reset

echo 重新获取IP地址...
ipconfig /release
ipconfig /renew

echo ✅ 基础网络诊断和重置完成
return

:: 内置连接测试功能
:builtin_connection_test
echo.
echo 📊 内置连接测试功能
echo ==========================================

set /p target_ip="请输入要测试的IP地址 (默认: ************): "
if "%target_ip%"=="" set target_ip=************

echo.
echo 🌐 测试到 %target_ip% 的连接...

echo 基础连通性测试:
ping -n 4 %target_ip%

echo.
echo RDP端口测试 (3389):
telnet %target_ip% 3389

echo.
echo 路由跟踪:
tracert -h 10 %target_ip%

echo ✅ 连接测试完成
return

:exit
pause
exit /b 0
