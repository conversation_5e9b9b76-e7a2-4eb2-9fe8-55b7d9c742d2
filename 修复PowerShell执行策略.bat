@echo off
chcp 65001 >nul
title PowerShell执行策略修复工具

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 需要管理员权限运行此脚本
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ==========================================
echo 🔧 PowerShell执行策略修复工具
echo ==========================================
echo.

echo 当前PowerShell执行策略:
PowerShell -Command "Get-ExecutionPolicy -List"
echo.

echo 选择操作:
echo.
echo 1. 临时允许脚本执行 (当前用户)
echo 2. 永久允许脚本执行 (当前用户) 
echo 3. 恢复默认安全设置
echo 4. 查看当前策略
echo 5. 退出
echo.

set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto temp_allow
if "%choice%"=="2" goto permanent_allow
if "%choice%"=="3" goto restore_default
if "%choice%"=="4" goto show_policy
if "%choice%"=="5" goto exit

echo ❌ 无效选择
goto start

:temp_allow
echo.
echo 🔧 设置临时执行策略 (RemoteSigned - 当前用户)...
PowerShell -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force"
if %errorlevel% equ 0 (
    echo ✅ 执行策略已临时更改
    echo 💡 现在可以运行PowerShell脚本了
    echo ⚠️  建议使用完毕后运行选项3恢复安全设置
) else (
    echo ❌ 执行策略更改失败
)
goto end

:permanent_allow
echo.
echo 🔧 设置永久执行策略 (RemoteSigned - 当前用户)...
echo ⚠️  这将永久允许运行本地和已签名的远程脚本
set /p confirm="确定要继续吗? (Y/N): "
if /i not "%confirm%"=="Y" goto end

PowerShell -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force"
if %errorlevel% equ 0 (
    echo ✅ 执行策略已永久更改
    echo 💡 现在可以运行PowerShell脚本了
) else (
    echo ❌ 执行策略更改失败
)
goto end

:restore_default
echo.
echo 🔒 恢复默认安全设置 (Restricted)...
PowerShell -Command "Set-ExecutionPolicy -ExecutionPolicy Restricted -Scope CurrentUser -Force"
if %errorlevel% equ 0 (
    echo ✅ 执行策略已恢复为默认安全设置
    echo 🔒 PowerShell脚本执行已被禁用
) else (
    echo ❌ 执行策略恢复失败
)
goto end

:show_policy
echo.
echo 📊 当前PowerShell执行策略:
PowerShell -Command "Get-ExecutionPolicy -List | Format-Table -AutoSize"
echo.
echo 策略说明:
echo - Restricted: 禁止执行任何脚本 (默认)
echo - RemoteSigned: 允许本地脚本，远程脚本需要签名
echo - Unrestricted: 允许所有脚本 (不推荐)
goto end

:end
echo.
echo ==========================================
echo 💡 使用建议:
echo ==========================================
echo.
echo 🔧 运行网络优化脚本:
echo 1. 选择选项1临时允许脚本执行
echo 2. 运行 "启动网络优化工具.bat"
echo 3. 完成后选择选项3恢复安全设置
echo.
echo 🔧 或者直接使用绕过方式:
echo PowerShell -ExecutionPolicy Bypass -File "脚本名.ps1"
echo.

:exit
pause
exit /b 0
