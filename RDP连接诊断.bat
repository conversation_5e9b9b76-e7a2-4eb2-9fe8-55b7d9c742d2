@echo off
chcp 65001 >nul
title 远程桌面连接诊断工具

echo ==========================================
echo 远程桌面连接诊断工具
echo ==========================================
echo.

set /p target_ip="请输入远程服务器IP地址 (默认: ************): "
if "%target_ip%"=="" set target_ip=************

echo.
echo 开始诊断连接到 %target_ip% 的问题...
echo.

echo ==========================================
echo 1. 基础网络连通性测试
echo ==========================================
echo 正在ping服务器...
ping -n 4 %target_ip%
if %errorlevel% neq 0 (
    echo ❌ 基础网络连接失败！
    echo 可能原因：
    echo - 网络连接问题
    echo - 目标服务器离线
    echo - 防火墙阻止ICMP
    echo.
) else (
    echo ✅ 基础网络连接正常
    echo.
)

echo ==========================================
echo 2. RDP端口连通性测试 (端口3389)
echo ==========================================
echo 正在测试RDP端口...
telnet %target_ip% 3389 2>nul
if %errorlevel% neq 0 (
    echo ❌ RDP端口(3389)连接失败！
    echo 可能原因：
    echo - RDP服务未启动
    echo - 防火墙阻止3389端口
    echo - 端口被修改为其他值
    echo.
) else (
    echo ✅ RDP端口连接正常
    echo.
)

echo ==========================================
echo 3. 本地网络配置检查
echo ==========================================
echo 本地IP配置：
ipconfig | findstr /i "ipv4\|默认网关\|子网掩码"
echo.

echo DNS配置：
ipconfig /all | findstr /i "dns"
echo.

echo ==========================================
echo 4. 路由跟踪测试
echo ==========================================
echo 正在跟踪到目标服务器的路由...
tracert -h 10 %target_ip%
echo.

echo ==========================================
echo 5. 本地RDP客户端检查
echo ==========================================
echo 检查远程桌面服务状态...
sc query TermService | findstr /i "state"
echo.

echo 检查RDP相关注册表设置...
reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Terminal Server" /v fDenyTSConnections 2>nul
if %errorlevel% equ 0 (
    echo ✅ 找到RDP注册表配置
) else (
    echo ⚠️  未找到RDP注册表配置
)
echo.

echo ==========================================
echo 6. 网络适配器状态
echo ==========================================
echo 活跃的网络适配器：
wmic path win32_networkadapter where "NetConnectionStatus=2" get Name,NetConnectionID,Speed /format:table
echo.

echo ==========================================
echo 7. 防火墙状态检查
echo ==========================================
echo Windows防火墙状态：
netsh advfirewall show allprofiles state
echo.

echo 检查RDP防火墙规则：
netsh advfirewall firewall show rule name="远程桌面*" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  未找到远程桌面防火墙规则，可能被阻止
) else (
    echo ✅ 找到远程桌面防火墙规则
)
echo.

echo ==========================================
echo 8. 系统资源检查
echo ==========================================
echo CPU和内存使用情况：
wmic cpu get loadpercentage /value | findstr LoadPercentage
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value | findstr /i memory
echo.

echo ==========================================
echo 诊断建议
echo ==========================================
echo.
echo 如果连接仍然不稳定，请尝试以下解决方案：
echo.
echo 🔧 网络层面：
echo - 更换DNS服务器 (8.8.8.8, 114.114.114.114)
echo - 重启路由器/调制解调器
echo - 使用有线连接替代WiFi
echo - 联系网络服务提供商检查线路
echo.
echo 🔧 系统层面：
echo - 重启远程桌面服务：net stop TermService ^&^& net start TermService
echo - 清理临时文件和重启系统
echo - 更新网络驱动程序
echo - 禁用电源管理中的"允许计算机关闭此设备"
echo.
echo 🔧 RDP设置：
echo - 在服务器端增加会话超时时间
echo - 调整RDP连接数限制
echo - 使用不同的RDP客户端软件
echo - 降低颜色深度和分辨率
echo.
echo 🔧 高级选项：
echo - 修改RDP端口号避开常见攻击
echo - 配置VPN连接提高稳定性
echo - 使用第三方远程控制软件作为备选
echo.

echo ==========================================
echo 诊断完成！
echo ==========================================
echo 建议将此诊断结果保存，并根据发现的问题进行相应处理。
echo.
pause
